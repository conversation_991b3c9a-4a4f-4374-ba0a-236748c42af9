# 意图管理快速参考卡片

## 🚀 常用操作

### 添加新意图
```bash
# 1. 编辑配置文件
vim backend/config/intent_definitions.yaml

# 2. 更新模板
python -c "from backend.utils.template_synchronizer import TemplateSynchronizer; TemplateSynchronizer().update_template()"

# 3. 验证同步
python -c "from backend.utils.template_synchronizer import TemplateSynchronizer; print('同步' if TemplateSynchronizer().validate_template_sync()[0] else '不同步')"
```

### 检查系统状态
```bash
# 快速健康检查
python -c "
from backend.utils.intent_manager import IntentManager
from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.utils.template_synchronizer import TemplateSynchronizer

# 检查配置
manager = IntentManager()
print(f'✅ 配置加载: {len(manager.get_valid_intents())} 个意图')

# 检查决策引擎
engine = SimplifiedDecisionEngine()
print(f'✅ 决策引擎: {\"正常\" if engine.intent_manager else \"备用模式\"}')

# 检查模板同步
sync = TemplateSynchronizer()
is_synced, _ = sync.validate_template_sync()
print(f'✅ 模板同步: {\"正常\" if is_synced else \"需要更新\"}')
"
```

### 故障修复
```bash
# 修复模板不同步
python -c "from backend.utils.template_synchronizer import TemplateSynchronizer; TemplateSynchronizer().update_template(); print('修复完成')"

# 验证配置完整性
python -c "from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine; SimplifiedDecisionEngine(); print('配置正常')"
```

## 📁 关键文件位置

| 文件 | 路径 | 作用 |
|------|------|------|
| 统一配置 | `backend/config/intent_definitions.yaml` | 意图定义的单一数据源 |
| 意图管理器 | `backend/utils/intent_manager.py` | 配置读取和管理 |
| 模板同步器 | `backend/utils/template_synchronizer.py` | 模板自动生成和同步 |
| 决策引擎 | `backend/agents/simplified_decision_engine.py` | 集成了配置驱动逻辑 |
| 意图模板 | `backend/prompts/intent_recognition.md` | 自动生成的LLM模板 |

## ⚠️ 重要提醒

### ✅ 应该做的
- 修改配置后立即更新模板
- 定期运行同步验证
- 重要修改前先备份
- 使用版本控制管理配置

### ❌ 不应该做的
- 直接编辑 `intent_recognition.md`
- 在代码中硬编码意图列表
- 忽略启动时的警告信息
- 跳过同步验证步骤

## 🔧 配置文件格式

```yaml
intent_system:
  version: "1.0"
  intents:
    intent_name:
      description: "意图描述"
      examples: ["示例1", "示例2"]
      action: "处理动作"
      priority: 1
      supported_states: ["IDLE"]
```

## 📞 紧急联系

- **配置问题**: 查看 `docs/意图管理统一化维护指南.md`
- **测试验证**: 运行 `test_end_to_end_integration.py`
- **性能问题**: 运行 `test_performance_benchmark.py`
